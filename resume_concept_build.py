#!/usr/bin/env python3
"""
恢复概念索引构建
从中断点继续构建，支持智能暂停和恢复
"""

import os
import json
import time
from build_concept_index import ConceptIndexBuilder

def resume_build():
    """恢复构建"""
    
    print("🔄 恢复概念索引构建")
    print("=" * 50)
    
    # 检查是否有断点续传文件
    resume_file = "concept_index.json.resume"
    if not os.path.exists(resume_file):
        print("❌ 未找到断点续传文件，请先运行 build_concept_index.py")
        return
    
    # 显示当前进度
    try:
        with open(resume_file, 'r', encoding='utf-8') as f:
            processed = json.load(f)
        print(f"📊 已处理概念数量: {len(processed)}")
        print(f"📁 断点续传文件: {resume_file}")
    except Exception as e:
        print(f"❌ 读取断点续传文件失败: {e}")
        return
    
    # 询问用户是否继续
    response = input("\n是否继续构建? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return
    
    # 创建构建器并继续
    builder = ConceptIndexBuilder(
        output_file="concept_index.json",
        fetch_interval=3.0  # 更保守的间隔
    )
    
    try:
        print("\n🚀 继续构建...")
        success = builder.build_index()
        
        if success:
            print("\n🎉 构建完成!")
        else:
            print("\n💥 构建失败")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断构建")
        print("💡 可以再次运行此脚本继续")
    except Exception as e:
        print(f"\n💥 构建过程中出现异常: {e}")

def check_progress():
    """检查构建进度"""
    
    print("📊 检查构建进度")
    print("=" * 30)
    
    # 检查断点续传文件
    resume_file = "concept_index.json.resume"
    if os.path.exists(resume_file):
        try:
            with open(resume_file, 'r', encoding='utf-8') as f:
                processed = json.load(f)
            print(f"✅ 已处理概念: {len(processed)} 个")
        except Exception as e:
            print(f"❌ 读取断点续传文件失败: {e}")
    else:
        print("❌ 未找到断点续传文件")
    
    # 检查输出文件
    output_file = "concept_index.json"
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'metadata' in data:
                meta = data['metadata']
                print(f"📁 输出文件存在:")
                print(f"   构建时间: {meta.get('build_time', 'N/A')}")
                print(f"   总概念数: {meta.get('total_concepts', 'N/A')}")
                print(f"   成功处理: {meta.get('success_concepts', 'N/A')}")
                print(f"   覆盖股票: {meta.get('total_stocks', 'N/A')}")
            else:
                print(f"📁 输出文件存在但格式异常")
                
        except Exception as e:
            print(f"❌ 读取输出文件失败: {e}")
    else:
        print("❌ 输出文件不存在")

def clean_resume():
    """清理断点续传文件"""
    
    resume_file = "concept_index.json.resume"
    if os.path.exists(resume_file):
        response = input(f"确定要删除断点续传文件 {resume_file}? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            os.remove(resume_file)
            print("✅ 断点续传文件已删除")
        else:
            print("❌ 操作取消")
    else:
        print("❌ 断点续传文件不存在")

def main():
    """主菜单"""
    
    while True:
        print("\n" + "=" * 50)
        print("📊 概念索引构建管理器")
        print("=" * 50)
        print("1. 恢复构建")
        print("2. 检查进度") 
        print("3. 清理断点续传文件")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            resume_build()
        elif choice == '2':
            check_progress()
        elif choice == '3':
            clean_resume()
        elif choice == '4':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
