#!/usr/bin/env python3
"""
离线构建概念股索引文件
独立运行脚本，用于生成 concept_index.json 文件
"""

import os
import json
import time
import logging
import sys
from datetime import datetime
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ConceptIndexBuilder:
    """概念股索引构建器"""
    
    def __init__(self, output_file="concept_index.json", fetch_interval=0.5, resume_file=None):
        self.output_file = output_file
        self.fetch_interval = fetch_interval
        self.resume_file = resume_file or f"{output_file}.resume"
        self.setup_logging()
        self.setup_http_session()

        # 加载断点续传数据
        self.processed_concepts = self.load_resume_data()
        
    def setup_logging(self):
        """设置日志"""
        # 清除现有处理器
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
            
        # 配置日志输出到控制台和文件
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'concept_build_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
            ],
            force=True
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_http_session(self):
        """设置HTTP会话"""
        self.session = requests.Session()
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,
            pool_maxsize=20,
            pool_block=False
        )
        
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
        })
        
    def safe_akshare_call(self, func, *args, max_retries=3, **kwargs):
        """安全的akshare调用"""
        func_name = getattr(func, '__name__', str(func))
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"📞 调用 {func_name} (尝试 {attempt + 1}/{max_retries})")
                
                # 重新导入akshare
                import akshare as ak
                result = func(*args, **kwargs)
                
                if hasattr(result, 'shape'):
                    self.logger.info(f"✅ {func_name} 成功，数据形状: {result.shape}")
                else:
                    self.logger.info(f"✅ {func_name} 成功")
                    
                return result
                
            except Exception as e:
                error_msg = str(e).lower()
                
                if any(keyword in error_msg for keyword in [
                    'connection', 'remote end closed', 'timeout',
                    'read timeout', 'connection aborted', 'max retries',
                    'network', 'unreachable', 'refused', 'reset'
                ]):
                    self.logger.warning(f"🔄 连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                    
                    if attempt < max_retries - 1:
                        wait_time = (2 ** attempt) * self.fetch_interval * 2
                        self.logger.info(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                        time.sleep(wait_time)
                        self.setup_http_session()  # 重置会话
                        continue
                    else:
                        self.logger.error(f"❌ {func_name} 最大重试次数已达到")
                        raise
                else:
                    self.logger.error(f"❌ {func_name} 非连接错误: {e}")
                    raise
                    
        return None

    def load_resume_data(self):
        """加载断点续传数据"""
        try:
            if os.path.exists(self.resume_file):
                with open(self.resume_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"📂 加载断点续传数据: 已处理 {len(data)} 个概念")
                    return set(data)
        except Exception as e:
            self.logger.warning(f"⚠️ 无法加载断点续传数据: {e}")
        return set()

    def save_resume_data(self):
        """保存断点续传数据"""
        try:
            with open(self.resume_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.processed_concepts), f, ensure_ascii=False, indent=2)
            self.logger.info(f"💾 保存断点续传数据: {len(self.processed_concepts)} 个概念")
        except Exception as e:
            self.logger.warning(f"⚠️ 无法保存断点续传数据: {e}")

    def cleanup_resume_data(self):
        """清理断点续传数据"""
        try:
            if os.path.exists(self.resume_file):
                os.remove(self.resume_file)
                self.logger.info("🧹 清理断点续传文件")
        except Exception as e:
            self.logger.warning(f"⚠️ 无法清理断点续传文件: {e}")

    def build_index(self):
        """构建概念股索引"""
        self.logger.info("🚀 开始构建概念股索引...")
        
        try:
            # 导入akshare
            import akshare as ak
            
            # 获取概念列表
            self.logger.info("📋 获取概念板块列表...")
            concept_df = self.safe_akshare_call(ak.stock_board_concept_name_em)
            
            if concept_df is None or concept_df.empty:
                self.logger.error("❌ 无法获取概念板块列表")
                return False
                
            num_concepts = len(concept_df)
            self.logger.info(f"📊 找到 {num_concepts} 个概念板块")
            
            # 构建索引
            concept_index = {}
            success_count = 0
            error_count = 0
            processed_count = len(self.processed_concepts)  # 已处理的概念数
            start_time = time.time()

            for i, row in concept_df.iterrows():
                concept_name = row['板块名称']
                concept_code = row['板块代码']

                # 检查是否已处理过（断点续传）
                if concept_code in self.processed_concepts:
                    self.logger.info(f"⏭️ 跳过已处理的概念: {concept_name} ({concept_code})")
                    continue

                # 计算进度（基于实际处理的概念）
                current_processed = processed_count + success_count + error_count + 1
                progress = f"{current_processed}/{num_concepts}"
                remaining = num_concepts - current_processed
                elapsed = time.time() - start_time
                avg_time = elapsed / (success_count + error_count + 1) if (success_count + error_count) > 0 else 0
                eta = avg_time * remaining if avg_time > 0 else 0

                self.logger.info(f"🔍 处理概念: {concept_name} ({concept_code}) - {progress}")
                print(f"🔍 [{progress}] {concept_name} (剩余: {remaining}, 预计: {eta/60:.1f}分钟)")
                
                try:
                    # 获取概念成分股
                    cons_df = self.safe_akshare_call(ak.stock_board_concept_cons_em, symbol=concept_code)
                    
                    if cons_df is not None and not cons_df.empty:
                        stock_count = 0
                        for stock_code in cons_df['代码']:
                            stock_code_cleaned = str(stock_code).zfill(6)[:6]
                            concept_index.setdefault(stock_code_cleaned, []).append(concept_name)
                            stock_count += 1
                            
                        self.logger.info(f"✅ {concept_name}: 添加了 {stock_count} 只股票")
                        success_count += 1
                    else:
                        self.logger.warning(f"⚠️ {concept_name}: 未找到成分股")

                    # 标记为已处理
                    self.processed_concepts.add(concept_code)

                    # 每处理10个概念保存一次断点续传数据
                    if len(self.processed_concepts) % 10 == 0:
                        self.save_resume_data()

                    # 礼貌等待
                    time.sleep(self.fetch_interval)
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    self.logger.error(f"❌ 处理概念 {concept_name} 时出错: {e}")
                    error_count += 1

                    # 检查是否是连接错误
                    if any(keyword in error_msg for keyword in [
                        'connection', 'remote end closed', 'timeout',
                        'read timeout', 'connection aborted', 'max retries'
                    ]):
                        # 连接错误，可能被封禁，暂停更长时间
                        pause_time = 30 * 60  # 30分钟
                        self.logger.warning(f"🚫 检测到可能的IP封禁，暂停 {pause_time/60:.0f} 分钟...")
                        print(f"🚫 检测到可能的IP封禁，暂停 {pause_time/60:.0f} 分钟...")

                        # 保存当前进度
                        self.save_resume_data()

                        # 暂停
                        time.sleep(pause_time)

                        # 重置HTTP会话
                        self.setup_http_session()

                        # 继续下一个概念，不标记当前概念为已处理
                        continue
                    else:
                        # 其他错误，标记为已处理避免重复尝试
                        self.processed_concepts.add(concept_code)
                        time.sleep(self.fetch_interval * 3)
                    
            # 保存结果
            self.logger.info(f"💾 保存索引到 {self.output_file}...")
            
            # 添加元数据
            result = {
                'metadata': {
                    'build_time': datetime.now().isoformat(),
                    'total_concepts': num_concepts,
                    'success_concepts': success_count,
                    'error_concepts': error_count,
                    'total_stocks': len(concept_index)
                },
                'data': concept_index
            }
            
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"✅ 索引构建完成!")
            self.logger.info(f"📈 统计信息:")
            self.logger.info(f"   - 总概念数: {num_concepts}")
            self.logger.info(f"   - 成功处理: {success_count}")
            self.logger.info(f"   - 处理失败: {error_count}")
            self.logger.info(f"   - 覆盖股票: {len(concept_index)}")
            
            print(f"\n✅ 索引构建完成! 文件保存为: {self.output_file}")
            print(f"📈 覆盖了 {len(concept_index)} 只股票的概念信息")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 构建索引时发生致命错误: {e}", exc_info=True)
            return False
            
    def __del__(self):
        """清理资源"""
        try:
            if hasattr(self, 'session'):
                self.session.close()
        except:
            pass

def main():
    """主函数"""
    print("=" * 60)
    print("📊 概念股索引构建器")
    print("=" * 60)

    # 检查akshare是否可用
    try:
        import akshare as ak
        print("✅ akshare 模块已安装")
    except ImportError:
        print("❌ 请先安装 akshare: pip install akshare")
        print("   安装命令: pip install akshare")
        return

    # 检查是否存在旧的索引文件
    if os.path.exists("concept_index.json"):
        print("⚠️ 发现已存在的 concept_index.json 文件")
        response = input("是否覆盖? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return

    # 创建构建器
    builder = ConceptIndexBuilder(
        output_file="concept_index.json",
        fetch_interval=2.0  # 2秒间隔，更保守
    )

    try:
        # 开始构建
        print("\n🚀 开始构建概念索引...")
        success = builder.build_index()

        if success:
            print("\n🎉 构建成功!")
            print(f"📁 文件位置: {os.path.abspath('concept_index.json')}")

            # 显示文件大小
            file_size = os.path.getsize("concept_index.json") / 1024 / 1024
            print(f"📊 文件大小: {file_size:.2f} MB")
        else:
            print("\n💥 构建失败，请检查日志文件")

    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断构建")
        print("💡 下次运行时会从中断点继续（断点续传）")
    except Exception as e:
        print(f"\n💥 构建过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
