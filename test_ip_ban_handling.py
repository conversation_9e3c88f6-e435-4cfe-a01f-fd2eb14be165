#!/usr/bin/env python3
"""
测试IP封禁处理逻辑
"""

import os
import json
import time
from unittest.mock import patch, MagicMock
from build_concept_index import ConceptIndexBuilder

def test_ip_ban_data_saving():
    """测试IP封禁时的数据保存"""
    
    print("🧪 测试IP封禁时的数据保存")
    print("=" * 40)
    
    # 创建测试构建器
    test_output = "test_ip_ban_output.json"
    builder = ConceptIndexBuilder(test_output)
    
    # 模拟已获取的部分数据
    test_concept_index = {
        "000001": ["银行概念", "深圳本地股"],
        "000002": ["房地产概念"],
        "600000": ["银行概念", "上海本地股"]
    }
    
    try:
        # 测试保存部分结果
        builder._save_partial_results(test_concept_index, 3, 0, 435)
        
        # 验证文件是否正确保存
        if os.path.exists(test_output):
            with open(test_output, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            metadata = saved_data.get('metadata', {})
            data = saved_data.get('data', {})
            
            # 检查元数据
            if metadata.get('build_status') == 'partial':
                print("   ✅ 正确标记为部分结果")
            else:
                print(f"   ❌ 状态标记错误: {metadata.get('build_status')}")
                return False
            
            if metadata.get('success_concepts') == 3:
                print("   ✅ 成功概念数量正确")
            else:
                print(f"   ❌ 成功概念数量错误: {metadata.get('success_concepts')}")
                return False
            
            if metadata.get('total_stocks') == 3:
                print("   ✅ 总股票数量正确")
            else:
                print(f"   ❌ 总股票数量错误: {metadata.get('total_stocks')}")
                return False
            
            # 检查数据完整性
            if data == test_concept_index:
                print("   ✅ 数据保存完整")
            else:
                print("   ❌ 数据保存不完整")
                return False
            
            # 检查提示信息
            if 'IP封禁' in metadata.get('note', ''):
                print("   ✅ 包含IP封禁提示")
            else:
                print("   ❌ 缺少IP封禁提示")
                return False
            
            return True
        else:
            print("   ❌ 文件未保存")
            return False
            
    finally:
        # 清理测试文件
        if os.path.exists(test_output):
            os.remove(test_output)

def test_ip_ban_detection_and_exit():
    """测试IP封禁检测和退出逻辑"""
    
    print("\n🧪 测试IP封禁检测和退出逻辑")
    print("=" * 40)
    
    test_output = "test_ip_ban_exit.json"
    test_resume = f"{test_output}.resume"
    
    try:
        # 创建测试构建器
        builder = ConceptIndexBuilder(test_output)
        
        # 模拟akshare调用失败（连接错误）
        def mock_concept_stock(*args, **kwargs):
            raise ConnectionError("Connection aborted")
        
        # 模拟概念列表
        mock_concepts = [
            ["银行概念", "BK0001"],
            ["房地产概念", "BK0002"]
        ]
        
        with patch('akshare.concept_name_em', return_value=type('MockDF', (), {
            'values': mock_concepts,
            'shape': (2, 2)
        })()):
            with patch('akshare.concept_stock_em', side_effect=mock_concept_stock):
                # 运行构建，应该在第一个概念就检测到IP封禁
                result = builder.build_index()
                
                # 验证返回False（表示因封禁中断）
                if result is False:
                    print("   ✅ 正确返回False表示封禁中断")
                else:
                    print(f"   ❌ 返回值错误: {result}")
                    return False
                
                # 验证部分结果文件是否保存
                if os.path.exists(test_output):
                    with open(test_output, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if data['metadata']['build_status'] == 'partial':
                        print("   ✅ 部分结果文件正确保存")
                    else:
                        print("   ❌ 部分结果文件状态错误")
                        return False
                else:
                    print("   ❌ 部分结果文件未保存")
                    return False
                
                # 验证断点续传文件是否保存
                if os.path.exists(test_resume):
                    print("   ✅ 断点续传文件正确保存")
                else:
                    print("   ❌ 断点续传文件未保存")
                    return False
                
                return True
                
    finally:
        # 清理测试文件
        for file in [test_output, test_resume]:
            if os.path.exists(file):
                os.remove(file)

def test_complete_build_status():
    """测试完整构建的状态标记"""
    
    print("\n🧪 测试完整构建的状态标记")
    print("=" * 40)
    
    test_output = "test_complete_build.json"
    
    try:
        builder = ConceptIndexBuilder(test_output)
        
        # 模拟成功的akshare调用
        mock_concepts = [["测试概念", "BK0001"]]
        mock_stocks = type('MockDF', (), {
            'values': [["000001", "测试股票"]],
            'shape': (1, 2)
        })()
        
        with patch('akshare.concept_name_em', return_value=type('MockDF', (), {
            'values': mock_concepts,
            'shape': (1, 2)
        })()):
            with patch('akshare.concept_stock_em', return_value=mock_stocks):
                # 运行完整构建
                result = builder.build_index()
                
                # 验证返回True（表示成功完成）
                if result is True:
                    print("   ✅ 正确返回True表示构建完成")
                else:
                    print(f"   ❌ 返回值错误: {result}")
                    return False
                
                # 验证结果文件状态
                if os.path.exists(test_output):
                    with open(test_output, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if data['metadata']['build_status'] == 'complete':
                        print("   ✅ 正确标记为完整结果")
                        return True
                    else:
                        print(f"   ❌ 状态标记错误: {data['metadata']['build_status']}")
                        return False
                else:
                    print("   ❌ 结果文件未保存")
                    return False
                    
    finally:
        # 清理测试文件
        if os.path.exists(test_output):
            os.remove(test_output)

def main():
    """主测试函数"""
    
    print("🚀 开始测试IP封禁处理逻辑")
    print("=" * 60)
    
    tests = [
        ("IP封禁时数据保存", test_ip_ban_data_saving),
        ("IP封禁检测和退出", test_ip_ban_detection_and_exit),
        ("完整构建状态标记", test_complete_build_status),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ 测试失败: {test_name}")
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {e}")
    
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! IP封禁处理逻辑正确。")
        print("\n📋 修复总结:")
        print("   ✅ IP封禁时立即保存已获取的数据")
        print("   ✅ 保存断点续传文件用于恢复")
        print("   ✅ 明确提示等待24小时后继续")
        print("   ✅ 区分部分结果和完整结果")
        print("   ✅ 避免无效的30分钟等待循环")
    else:
        print("❌ 部分测试失败，需要进一步修复。")

if __name__ == "__main__":
    main()
