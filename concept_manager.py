import os
import json
import time
import threading
import datetime
import logging # 引入 logging
import requests
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

class ConceptManager: # 将概念相关的逻辑独立出来，或者放在StockAnalyzer里
    def __init__(self, cache_file="concept_index.json", fetch_interval_seconds=0.5, offline_mode=False):
        # 设置日志
        logging.basicConfig(level=logging.INFO,
                            format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)

        self.CONCEPT_CACHE_FILE = cache_file
        self.FETCH_INTERVAL_SECONDS = fetch_interval_seconds # 获取每个概念成分股的间隔
        self.offline_mode = offline_mode  # 离线模式标志
        self._concept_index_data = {} # 内存中的索引副本
        self._index_lock = threading.Lock() # 用于保护对 _concept_index_data 的并发访问
        self._last_successful_build_time = 0 # 上次成功构建的时间戳

        # 只在非离线模式下配置HTTP会话
        if not self.offline_mode:
            self._setup_http_session()

        # 启动时加载一次索引
        self.load_or_build_initial_index()

    def _setup_http_session(self):
        """设置HTTP会话和重试策略"""
        self.session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=3,  # 总重试次数
            backoff_factor=1,  # 退避因子
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
            allowed_methods=["HEAD", "GET", "OPTIONS"]  # 允许重试的HTTP方法
        )

        # 配置HTTP适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,  # 连接池大小
            pool_maxsize=20,  # 连接池最大连接数
            pool_block=False  # 连接池满时不阻塞
        )

        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        self.logger.info("HTTP session configured with retry strategy")

        # 启动时加载一次索引
        self.load_or_build_initial_index()

    def _safe_akshare_call(self, func, *args, max_retries=3, **kwargs):
        """安全的akshare调用，包含重试和错误处理"""
        for attempt in range(max_retries):
            try:
                # 在每次调用前重新导入akshare，确保连接状态
                import akshare as ak

                # 调用akshare函数
                result = func(*args, **kwargs)

                # 成功则返回结果
                return result

            except Exception as e:
                error_msg = str(e).lower()

                # 检查是否是连接相关错误
                if any(keyword in error_msg for keyword in [
                    'connection', 'remote end closed', 'timeout',
                    'read timeout', 'connection aborted', 'max retries'
                ]):
                    self.logger.warning(f"Connection error on attempt {attempt + 1}/{max_retries}: {e}")

                    if attempt < max_retries - 1:
                        # 指数退避：2^attempt * 基础间隔
                        wait_time = (2 ** attempt) * self.FETCH_INTERVAL_SECONDS * 2
                        self.logger.info(f"Waiting {wait_time:.1f} seconds before retry...")
                        time.sleep(wait_time)

                        # 重置HTTP会话
                        self._setup_http_session()
                        continue
                    else:
                        self.logger.error(f"Max retries reached for akshare call: {e}")
                        raise
                else:
                    # 非连接错误，直接抛出
                    self.logger.error(f"Non-connection error in akshare call: {e}")
                    raise

        return None

    def _fetch_and_build_index_logic(self):
        """
        核心的获取和构建索引的逻辑，不包含文件加载和保存。
        返回构建好的索引数据，如果失败则返回 None。
        """
        self.logger.info("Starting to fetch and build concept index from AkShare...")
        import akshare as ak # 动态导入，避免在不需要时加载
        actual_fetched_index = {}
        try:
            # 使用安全调用获取概念列表
            concept_df = self._safe_akshare_call(ak.stock_board_concept_name_em)
            if concept_df is None or concept_df.empty:
                self.logger.warning("Failed to fetch concept names from AkShare.")
                return None

            num_concepts = len(concept_df)
            self.logger.info(f"Found {num_concepts} concepts to process.")

            for i, row in concept_df.iterrows():
                concept_name = row['板块名称']
                concept_code = row['板块代码']
                self.logger.info(f"Processing concept: {concept_name} ({concept_code}) - {i+1}/{num_concepts}")
                try:
                    # 使用安全调用获取概念成分股
                    cons_df = self._safe_akshare_call(ak.stock_board_concept_cons_em, symbol=concept_code)
                    if cons_df is not None and not cons_df.empty:
                        for stock_code_in_concept in cons_df['代码']:
                            # 确保股票代码是标准6位
                            stock_code_cleaned = str(stock_code_in_concept).zfill(6)[:6]
                            actual_fetched_index.setdefault(stock_code_cleaned, []).append(concept_name)
                    else:
                        self.logger.debug(f"No constituents found for concept: {concept_name}")

                    # 礼貌抓取，避免IP被封
                    time.sleep(self.FETCH_INTERVAL_SECONDS)

                except Exception as e_cons:
                    self.logger.error(f"Error fetching constituents for concept {concept_name} ({concept_code}): {e_cons}")

            if not actual_fetched_index:
                self.logger.warning("Built concept index is empty. This might be an issue.")

            self.logger.info(f"Successfully fetched and built concept index. Stocks covered: {len(actual_fetched_index)}")
            return actual_fetched_index

        except Exception as e:
            self.logger.error(f"Fatal error during fetching and building concept index: {e}", exc_info=True)
            return None

    def force_refresh_and_save_index(self):
        """
        强制刷新索引，获取最新数据，并保存到文件和内存。
        这个方法由后台调度器调用。
        """
        self.logger.info("Force refresh: Attempting to build and save new concept index.")
        new_index_data = self._fetch_and_build_index_logic()

        if new_index_data is not None:
            try:
                # 保存到文件
                with open(self.CONCEPT_CACHE_FILE, 'w', encoding='utf-8') as f:
                    json.dump(new_index_data, f, ensure_ascii=False, indent=4)
                self.logger.info(f"Successfully saved new concept index to file: {self.CONCEPT_CACHE_FILE}")

                # 更新内存中的副本
                with self._index_lock:
                    self._concept_index_data = new_index_data
                    self._last_successful_build_time = time.time()
                self.logger.info("Successfully updated in-memory concept index.")
                return True
            except IOError as e:
                self.logger.error(f"IOError saving concept index to file {self.CONCEPT_CACHE_FILE}: {e}")
                return False
            except Exception as e:
                self.logger.error(f"Unexpected error saving concept index: {e}", exc_info=True)
                return False
        else:
            self.logger.error("Force refresh: Failed to build new concept index. In-memory and file cache not updated.")
            return False

    def load_or_build_initial_index(self):
        """
        程序启动时调用：尝试从文件加载，如果文件不存在、无效或过于陈旧，则构建一次。
        """
        loaded_from_file = False
        if os.path.exists(self.CONCEPT_CACHE_FILE):
            try:
                # 检查文件是否是今天的，如果不是今天的，也认为需要刷新（除非是刚过午夜）
                file_mod_timestamp = os.path.getmtime(self.CONCEPT_CACHE_FILE)
                file_mod_date = datetime.date.fromtimestamp(file_mod_timestamp)
                today = datetime.date.today()

                # 如果文件是昨天的，且现在还没到例如凌晨1点（给刷新留足时间），可以暂时用昨天的
                # 简单起见，这里只要不是今天的文件，就倾向于在启动时重建一次，除非文件很新
                # 或者，可以设置一个更宽松的TTL，比如23小时，如果文件修改时间在23小时内，就加载
                if (time.time() - file_mod_timestamp) < (23 * 3600): # 23小时内有效
                    with open(self.CONCEPT_CACHE_FILE, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if data: # 确保加载的数据不是空的
                        with self._index_lock:
                            self._concept_index_data = data
                            self._last_successful_build_time = file_mod_timestamp
                        self.logger.info(f"Successfully loaded concept index from file: {self.CONCEPT_CACHE_FILE}")
                        loaded_from_file = True
                    else:
                        self.logger.warning(f"Concept index file {self.CONCEPT_CACHE_FILE} is empty. Will attempt to rebuild.")
                else:
                    self.logger.info(f"Concept index file {self.CONCEPT_CACHE_FILE} is older than 23 hours. Will attempt to rebuild.")

            except json.JSONDecodeError:
                self.logger.error(f"Error decoding JSON from {self.CONCEPT_CACHE_FILE}. File might be corrupted. Will attempt to rebuild.")
            except Exception as e:
                self.logger.error(f"Error loading concept index from file: {e}. Will attempt to rebuild.", exc_info=True)
        else:
            self.logger.info(f"Concept index file {self.CONCEPT_CACHE_FILE} not found. Will attempt to build.")

        if not loaded_from_file:
            self.logger.info("Initial build: Attempting to build and save concept index as file cache is missing or stale.")
            self.force_refresh_and_save_index() # 初始构建

    def get_stock_concepts(self, stock_code: str) -> list:
        """
        从内存中获取股票的概念。
        """
        raw_code = str(stock_code).zfill(6)[:6]
        with self._index_lock: # 读取也需要加锁，虽然 GIL 使字典读取原子性，但好习惯
            # 返回列表的副本，避免外部修改
            concepts = self._concept_index_data.get(raw_code, [])
            return list(concepts) if concepts else []

    def get_last_build_time_str(self):
        if self._last_successful_build_time > 0:
            return datetime.datetime.fromtimestamp(self._last_successful_build_time).strftime('%Y-%m-%d %H:%M:%S')
        return "Never built successfully or loaded."

# --- 后台调度逻辑 ---
_concept_manager_instance = None
_scheduler_thread = None
_stop_scheduler_event = threading.Event()

def _concept_scheduler_task(manager_instance: ConceptManager):
    # 设置日志
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    """后台线程执行的任务"""
    while not _stop_scheduler_event.is_set():
        now = datetime.datetime.now()
        # 计算到下一个零点的时间
        tomorrow = now.date() + datetime.timedelta(days=1)
        midnight_tomorrow = datetime.datetime.combine(tomorrow, datetime.time.min) # 明天0点0分0秒

        # 加上一点点延迟，比如0点过1分钟再执行，避免正好卡在0点
        target_run_time = midnight_tomorrow + datetime.timedelta(minutes=1)

        sleep_duration_seconds = (target_run_time - now).total_seconds()

        if sleep_duration_seconds <= 0: # 如果已经过了目标时间（例如程序刚启动在0点后）
            # 直接计算到后天的0点
            day_after_tomorrow = now.date() + datetime.timedelta(days=2)
            midnight_day_after_tomorrow = datetime.datetime.combine(day_after_tomorrow, datetime.time.min)
            target_run_time = midnight_day_after_tomorrow + datetime.timedelta(minutes=1)
            sleep_duration_seconds = (target_run_time - now).total_seconds()

        logger.info(f"Concept scheduler: Next refresh scheduled at {target_run_time.strftime('%Y-%m-%d %H:%M:%S')}. "
                    f"Sleeping for {sleep_duration_seconds:.0f} seconds.")

        # 等待指定时间，但允许被中断
        # 使用 event.wait() 可以被 _stop_scheduler_event.set() 中断
        awoken_by_event = _stop_scheduler_event.wait(timeout=sleep_duration_seconds)

        if awoken_by_event: # 如果是被 set() 唤醒，说明要停止了
            logger.info("Concept scheduler: Stop event received during sleep. Exiting.")
            break

        if _stop_scheduler_event.is_set(): # 再次检查，万一是在 wait 返回后、执行任务前被设置
            logger.info("Concept scheduler: Stop event received before task execution. Exiting.")
            break

        logger.info(f"Concept scheduler: Woke up. Current time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}. Attempting to refresh concept index.")
        try:
            manager_instance.force_refresh_and_save_index()
            logger.info(f"Concept scheduler: Refresh task completed. Last build time: {manager_instance.get_last_build_time_str()}")
        except Exception as e:
            logger.error(f"Concept scheduler: Error during scheduled refresh: {e}", exc_info=True)
            # 即使出错，也要继续下一次调度
            time.sleep(60) # 出错后稍微等待一下，避免快速连续失败

    logger.info("Concept scheduler thread finished.")


def start_concept_scheduler(manager_instance: ConceptManager):
    # 设置日志
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    """启动概念索引的后台刷新调度器"""
    global _scheduler_thread, _concept_manager_instance
    if _scheduler_thread is not None and _scheduler_thread.is_alive():
        logger.warning("Concept scheduler thread is already running.")
        return

    _concept_manager_instance = manager_instance
    _stop_scheduler_event.clear() # 重置停止事件

    _scheduler_thread = threading.Thread(
        target=_concept_scheduler_task,
        args=(manager_instance,),
        name="ConceptSchedulerThread",
        daemon=True # 设置为守护线程，主程序退出时它会自动退出
    )
    _scheduler_thread.start()
    logger.info("Concept scheduler thread initiated.")

def stop_concept_scheduler():
    """停止概念索引的后台刷新调度器"""
    # 设置日志
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)

    logger.info("Attempting to stop concept scheduler thread...")
    _stop_scheduler_event.set() # 设置停止事件
    if _scheduler_thread is not None and _scheduler_thread.is_alive():
        _scheduler_thread.join(timeout=10) # 等待线程结束，设置超时
        if _scheduler_thread.is_alive():
            logger.warning("Concept scheduler thread did not stop gracefully after 10 seconds.")
        else:
            logger.info("Concept scheduler thread stopped.")
    else:
        logger.info("Concept scheduler thread was not running or already stopped.")

    def __del__(self):
        """析构函数，确保资源正确释放"""
        try:
            if hasattr(self, 'session'):
                self.session.close()
        except Exception:
            pass  # 忽略析构时的错误