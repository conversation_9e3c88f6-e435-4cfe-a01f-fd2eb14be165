#!/usr/bin/env python3
"""
测试概念管理器的连接稳定性
"""

import logging
import time
from concept_manager import ConceptManager

def test_concept_manager_stability():
    """测试概念管理器的连接稳定性"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    logger.info("开始测试概念管理器连接稳定性...")
    
    try:
        # 创建概念管理器实例，设置较短的间隔进行测试
        concept_manager = ConceptManager(
            cache_file="test_concept_index.json",
            fetch_interval_seconds=1.0  # 1秒间隔
        )
        
        logger.info("概念管理器初始化完成")
        
        # 强制刷新索引来测试连接稳定性
        logger.info("开始强制刷新索引...")
        start_time = time.time()
        
        concept_manager.force_refresh_and_save_index()
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"索引刷新完成，耗时: {duration:.2f} 秒")
        
        # 检查结果
        index_data = concept_manager.get_concept_index()
        if index_data:
            logger.info(f"成功获取概念索引，包含 {len(index_data)} 只股票")
            
            # 显示前几个概念
            count = 0
            for stock_code, concepts in index_data.items():
                if count < 5:
                    logger.info(f"股票 {stock_code}: {concepts[:3]}...")  # 只显示前3个概念
                    count += 1
                else:
                    break
        else:
            logger.error("未能获取概念索引数据")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}", exc_info=True)
    
    logger.info("测试完成")

if __name__ == "__main__":
    test_concept_manager_stability()
