# 概念索引构建指南

## 问题分析

根据日志分析，发现在处理约300个概念后会遇到连接被拒绝的问题：
- 服务器检测到频繁请求后实施反爬虫机制
- 需要暂停30分钟到1小时后才能恢复
- 支持断点续传，避免重复工作

## 解决方案

### 1. 智能暂停机制
- 检测到连接错误时自动暂停30分钟
- 保存当前进度，支持断点续传
- 重置HTTP会话，清理连接状态

### 2. 使用方法

#### 首次构建
```bash
python build_concept_index.py
```

#### 恢复构建（推荐）
```bash
python resume_concept_build.py
```

### 3. 构建策略

#### 分批构建策略
由于服务器限制，建议分批进行：

1. **第一批**：运行到约300个概念时会自动暂停
2. **等待30分钟**：让服务器解除限制
3. **继续构建**：使用恢复脚本继续

#### 最佳实践
- 在非交易时间运行（晚上或周末）
- 使用稳定的网络环境
- 耐心等待，整个过程可能需要2-4小时

### 4. 文件说明

- `build_concept_index.py` - 主构建脚本
- `resume_concept_build.py` - 恢复管理器
- `concept_index.json` - 最终输出文件
- `concept_index.json.resume` - 断点续传文件
- `concept_build_YYYYMMDD_HHMMSS.log` - 详细日志

### 5. 故障排除

#### 连接被拒绝
```
Connection aborted, Remote end closed connection without response
```
**解决方案**：等待30分钟后使用恢复脚本继续

#### 进度丢失
**解决方案**：检查是否存在 `.resume` 文件，使用恢复脚本

#### 文件损坏
**解决方案**：删除 `.resume` 文件，重新开始构建

### 6. 预期结果

成功构建后将得到：
- 覆盖约4000-5000只股票
- 包含400+个概念分类
- 文件大小约2-5MB
- 完整的元数据信息

### 7. 使用示例

构建完成后，在主程序中使用：

```python
from concept_manager import ConceptManager

# 自动加载 concept_index.json
cm = ConceptManager()

# 获取股票概念
concepts = cm.get_stock_concepts("000001")
print(concepts)  # ['银行概念', '深圳本地股', ...]
```

## 总结

这个解决方案通过智能暂停和断点续传机制，可以稳定地构建完整的概念索引文件，避免了网络连接问题导致的构建失败。
